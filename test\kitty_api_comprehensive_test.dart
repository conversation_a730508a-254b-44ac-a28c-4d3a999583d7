import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/models/kitty_model.dart';
import 'package:onekitty/models/user_kitties_model.dart';
import 'package:onekitty/models/auth/user_model.dart';

/// Comprehensive test suite for Kitty API endpoints
/// Tests both "get all kitties" and user-specific kitties endpoints
/// Analyzes response structures and validates data handling
void main() {
  group('🐱 Comprehensive Kitty API Tests', () {
    late HttpService httpService;
    late KittyController kittyController;
    late UserKittyController userKittyController;
    late GetStorage storage;
    late Logger logger;

    setUpAll(() async {
      print('🚀 Initializing Kitty API Test Suite...');
      print('📅 Test started at: ${DateTime.now()}');
      print('=' * 60);
      
      // Initialize GetX for testing
      Get.testMode = true;
      
      // Initialize GetStorage for testing
      await GetStorage.init();
      storage = GetStorage();
      
      // Initialize Logger
      logger = Logger(
        printer: PrettyPrinter(
          methodCount: 2,
          errorMethodCount: 8,
          lineLength: 120,
          colors: true,
          printEmojis: true,
          printTime: true,
        ),
      );
      
      // Register dependencies
      Get.put<GetStorage>(storage);
      Get.put<Logger>(logger);
      
      // Initialize HTTP service
      httpService = HttpService();
      httpService.initializeDio();
      Get.put<HttpService>(httpService);
      
      // Initialize controllers
      kittyController = KittyController();
      userKittyController = UserKittyController();
      Get.put<KittyController>(kittyController);
      Get.put<UserKittyController>(userKittyController);
      
      print('✅ Test environment initialized successfully');
    });

    tearDownAll(() {
      print('🧹 Cleaning up test environment...');
      Get.reset();
      print('✅ Test suite completed at: ${DateTime.now()}');
      print('=' * 60);
    });

    group('📡 API Endpoint Tests', () {
      test('🌐 Test Base URL and API Configuration', () async {
        print('\n🔍 Testing API Configuration...');
        
        expect(ApiUrls.BASE_URL_DEV, isNotNull);
        expect(ApiUrls.get_kitty, equals("kitty/get_kitty_all/"));
        expect(ApiUrls.getUserKitties, equals("user/user_kitties/"));
        
        print('✅ Base URL: ${ApiUrls.BASE_URL_DEV}');
        print('✅ Get All Kitties Endpoint: ${ApiUrls.get_kitty}');
        print('✅ Get User Kitties Endpoint: ${ApiUrls.getUserKitties}');
        
        // Test HTTP service initialization
        expect(httpService.dio, isNotNull);
        expect(httpService.dio!.options.baseUrl, isNotEmpty);
        
        print('✅ HTTP Service initialized with base URL: ${httpService.dio!.options.baseUrl}');
      });

      test('🐱 Test Get All Kitties API Endpoint', () async {
        print('\n🔍 Testing Get All Kitties API...');
        
        try {
          final stopwatch = Stopwatch()..start();
          
          // Make API call to get all kitties
          final response = await httpService.request(
            url: ApiUrls.get_kitty,
            method: Method.GET,
          );
          
          stopwatch.stop();
          
          print('⏱️  API Response Time: ${stopwatch.elapsedMilliseconds}ms');
          print('📊 Response Status Code: ${response.statusCode}');
          print('📋 Response Headers: ${response.headers}');
          
          // Analyze response structure
          print('\n📊 RESPONSE ANALYSIS:');
          print('Response Data Type: ${response.data.runtimeType}');
          
          if (response.data is Map<String, dynamic>) {
            final data = response.data as Map<String, dynamic>;
            print('Response Keys: ${data.keys.toList()}');
            
            // Check for common response structure
            if (data.containsKey('status')) {
              print('✅ Status Field: ${data['status']}');
            }
            if (data.containsKey('message')) {
              print('✅ Message Field: ${data['message']}');
            }
            if (data.containsKey('data')) {
              print('✅ Data Field Type: ${data['data'].runtimeType}');
              
              if (data['data'] is List) {
                final kitties = data['data'] as List;
                print('✅ Kitties Count: ${kitties.length}');
                
                if (kitties.isNotEmpty) {
                  print('✅ First Kitty Structure: ${kitties.first.keys.toList()}');
                  
                  // Test data model parsing
                  try {
                    final firstKitty = Kitty.fromJson(kitties.first);
                    print('✅ Kitty Model Parsing: SUCCESS');
                    print('   - ID: ${firstKitty.iD}');
                    print('   - Title: ${firstKitty.title}');
                    print('   - Balance: ${firstKitty.balance}');
                    print('   - Status: ${firstKitty.status}');
                  } catch (e) {
                    print('❌ Kitty Model Parsing: FAILED - $e');
                  }
                }
              } else if (data['data'] is Map) {
                final dataMap = data['data'] as Map<String, dynamic>;
                print('✅ Data Map Keys: ${dataMap.keys.toList()}');
              }
            }
          }
          
          // Performance assertions
          expect(stopwatch.elapsedMilliseconds, lessThan(10000), 
                 reason: 'API should respond within 10 seconds');
          
          // Response structure assertions
          expect(response.statusCode, inInclusiveRange(200, 299),
                 reason: 'Should return successful status code');
          
          print('✅ Get All Kitties API test completed successfully');
          
        } catch (e, stackTrace) {
          print('❌ Get All Kitties API test failed: $e');
          print('Stack trace: $stackTrace');
          
          // Don't fail the test completely, just log the error
          // This allows us to see what's happening with the API
          print('⚠️  Continuing with other tests...');
        }
      });

      test('👤 Test Get User Kitties API Endpoint', () async {
        print('\n🔍 Testing Get User Kitties API...');
        
        // Set up a test user
        final testUser = UserModelLatest(
          phoneNumber: '************', // Test phone number
          firstName: 'Test',
          secondName: 'User',
        );

        userKittyController.user.value = testUser;
        
        try {
          final stopwatch = Stopwatch()..start();
          
          // Test the getUserkitties method
          await userKittyController.getUserkitties(page: 0, size: 10);
          
          stopwatch.stop();
          
          print('⏱️  User Kitties API Response Time: ${stopwatch.elapsedMilliseconds}ms');
          print('📊 API Status: ${userKittyController.status.value}');
          print('📋 API Message: ${userKittyController.apiMessage.value}');
          print('🐱 Kitties Count: ${userKittyController.kitties.length}');
          print('📄 Current Page: ${userKittyController.currentPage.value}');
          print('📊 Total Pages: ${userKittyController.totalPages.value}');
          print('🔢 Total Kitties: ${userKittyController.totalKitties.value}');
          
          // Analyze kitties data
          if (userKittyController.kitties.isNotEmpty) {
            print('\n📊 USER KITTIES ANALYSIS:');
            final firstKitty = userKittyController.kitties.first;
            print('✅ First Kitty Analysis:');
            print('   - Kitty Status: ${firstKitty.kittyStatus}');
            print('   - Kitty Type: ${firstKitty.kittyType}');
            print('   - Beneficiary Channel: ${firstKitty.kittBeneficiaryChannel}');
            print('   - Has Membership: ${firstKitty.hasMembership}');
            print('   - Has Signatories: ${firstKitty.hasSignatories}');
            print('   - Percentage: ${firstKitty.percentage}');
            
            if (firstKitty.kitty != null) {
              final kitty = firstKitty.kitty!;
              print('   - Kitty ID: ${kitty.iD}');
              print('   - Kitty Title: ${kitty.title}');
              print('   - Kitty Balance: ${kitty.balance}');
              print('   - Kitty Limit: ${kitty.limit}');
              print('   - End Date: ${kitty.endDate}');
              print('   - Settlement Type: ${kitty.settlementType}');
            }
          } else {
            print('ℹ️  No kitties found for test user');
          }
          
          // Performance assertions
          expect(stopwatch.elapsedMilliseconds, lessThan(15000),
                 reason: 'User kitties API should respond within 15 seconds');
          
          print('✅ Get User Kitties API test completed');
          
        } catch (e, stackTrace) {
          print('❌ Get User Kitties API test failed: $e');
          print('Stack trace: $stackTrace');
          print('⚠️  This might be expected if user doesn\'t exist or has no kitties');
        }
      });
    });

    group('🔍 Data Model Validation Tests', () {
      test('🏗️ Test Kitty Model Data Handling', () async {
        print('\n🔍 Testing Kitty Model Data Handling...');
        
        // Test with sample data
        final sampleKittyData = {
          'ID': 123,
          'CreatedAt': '2024-01-01T10:00:00Z',
          'UpdatedAt': '2024-01-02T10:00:00Z',
          'title': 'Test Kitty',
          'description': 'A test kitty for validation',
          'beneficiary_account': '**********',
          'beneficiary_channel': 'MPESA',
          'beneficiary_phone_number': '************',
          'end_date': '2024-12-31T23:59:59Z',
          'balance': 1000.50,
          'limit': 50000,
          'settlement_type': 1,
          'status': 1,
          'kitty_type': 1,
        };
        
        try {
          final kitty = Kitty.fromJson(sampleKittyData);
          
          print('✅ Kitty Model Validation:');
          print('   - ID: ${kitty.iD} (Expected: 123)');
          print('   - Title: ${kitty.title} (Expected: Test Kitty)');
          print('   - Balance: ${kitty.balance} (Expected: 1000.5)');
          print('   - Created At: ${kitty.createdAt}');
          print('   - End Date: ${kitty.endDate}');
          
          // Validate data integrity
          expect(kitty.iD, equals(123));
          expect(kitty.title, equals('Test Kitty'));
          expect(kitty.balance, equals(1000.50));
          expect(kitty.beneficiaryChannel, equals('MPESA'));
          expect(kitty.status, equals(1));
          
          // Test toJson conversion
          final jsonData = kitty.toJson();
          expect(jsonData, isA<Map<String, dynamic>>());
          expect(jsonData['ID'], equals(123));
          expect(jsonData['title'], equals('Test Kitty'));
          
          print('✅ Kitty model validation completed successfully');
          
        } catch (e) {
          print('❌ Kitty model validation failed: $e');
          fail('Kitty model should handle valid data correctly');
        }
      });

      test('🏗️ Test UserKitty Model Data Handling', () async {
        print('\n🔍 Testing UserKitty Model Data Handling...');
        
        final sampleUserKittyData = {
          'kitty': {
            'ID': 456,
            'title': 'User Test Kitty',
            'balance': 2000.75,
            'status': 1,
          },
          'kitty_status': 'active',
          'kitt_beneficiary_channel': 'BANK',
          'kitty_type': 'savings',
          'percentage': 15.5,
          'has_membership': true,
          'has_signatory_transaction': false,
          'payment_ref_label': 'Reference Number',
        };
        
        try {
          final userKitty = UserKitty.fromJson(sampleUserKittyData);
          
          print('✅ UserKitty Model Validation:');
          print('   - Kitty Status: ${userKitty.kittyStatus}');
          print('   - Kitty Type: ${userKitty.kittyType}');
          print('   - Percentage: ${userKitty.percentage}');
          print('   - Has Membership: ${userKitty.hasMembership}');
          print('   - Has Signatories: ${userKitty.hasSignatories}');
          print('   - Payment Ref Label: ${userKitty.paymentRefLabel}');
          
          // Validate nested kitty data
          expect(userKitty.kitty, isNotNull);
          expect(userKitty.kitty!.iD, equals(456));
          expect(userKitty.kitty!.title, equals('User Test Kitty'));
          expect(userKitty.percentage, equals(15.5));
          expect(userKitty.hasMembership, isTrue);
          expect(userKitty.hasSignatories, isFalse);
          
          print('✅ UserKitty model validation completed successfully');
          
        } catch (e) {
          print('❌ UserKitty model validation failed: $e');
          fail('UserKitty model should handle valid data correctly');
        }
      });
    });

    group('🔄 Pagination and Search Tests', () {
      test('📄 Test Pagination Functionality', () async {
        print('\n🔍 Testing Pagination Functionality...');

        // Set up test user
        final testUser = UserModelLatest(phoneNumber: '************');
        userKittyController.user.value = testUser;

        try {
          // Test first page
          await userKittyController.getUserkitties(page: 0, size: 5);
          final firstPageCount = userKittyController.kitties.length;
          final firstPageNumber = userKittyController.currentPage.value;

          print('✅ First Page Results:');
          print('   - Page Number: $firstPageNumber');
          print('   - Items Count: $firstPageCount');
          print('   - Total Pages: ${userKittyController.totalPages.value}');
          print('   - Total Kitties: ${userKittyController.totalKitties.value}');
          print('   - Is Last Page: ${userKittyController.isLastPage.value}');

          // Test loading more kitties if available
          if (!userKittyController.isLastPage.value && userKittyController.totalPages.value > 1) {
            await userKittyController.loadMoreKitties();
            final afterLoadMore = userKittyController.kitties.length;

            print('✅ After Load More:');
            print('   - Total Items: $afterLoadMore');
            print('   - Current Page: ${userKittyController.currentPage.value}');

            expect(afterLoadMore, greaterThanOrEqualTo(firstPageCount),
                   reason: 'Should have same or more items after loading more');
          }

          print('✅ Pagination test completed');

        } catch (e) {
          print('❌ Pagination test failed: $e');
          print('⚠️  This might be expected if user has no kitties');
        }
      });

      test('🔍 Test Search Functionality', () async {
        print('\n🔍 Testing Search Functionality...');

        // Set up test user
        final testUser = UserModelLatest(phoneNumber: '************');
        userKittyController.user.value = testUser;

        try {
          // Test search with a common term
          final searchTerms = ['kitty', 'test', 'savings', 'group'];

          for (final term in searchTerms) {
            print('🔍 Searching for: "$term"');

            final searchResult = await userKittyController.searchKitties(term);

            print('   - Search Result: $searchResult');
            print('   - Found Kitties: ${userKittyController.kitties.length}');
            print('   - Search Query: ${userKittyController.searchQuery.value}');
            print('   - Search Loading: ${userKittyController.searchLoading.value}');

            // Validate search results
            if (userKittyController.kitties.isNotEmpty) {
              print('   - First Result Title: ${userKittyController.kitties.first.kitty?.title}');
            }

            // Small delay between searches
            await Future.delayed(const Duration(milliseconds: 500));
          }

          // Test empty search (should reset to normal browsing)
          print('🔍 Testing empty search...');
          await userKittyController.searchKitties('');

          print('   - Empty Search Query: "${userKittyController.searchQuery.value}"');
          print('   - Kitties After Reset: ${userKittyController.kitties.length}');

          print('✅ Search functionality test completed');

        } catch (e) {
          print('❌ Search test failed: $e');
          print('⚠️  This might be expected if user has no kitties or search fails');
        }
      });
    });

    group('⚠️ Error Handling and Edge Cases', () {
      test('🚫 Test Invalid Data Handling', () async {
        print('\n🔍 Testing Invalid Data Handling...');

        // Test Kitty model with null/invalid data
        try {
          final invalidKittyData = {
            'ID': null,
            'title': null,
            'balance': 'invalid_number',
            'CreatedAt': 'invalid_date',
          };

          final kitty = Kitty.fromJson(invalidKittyData);

          print('✅ Kitty Model Null Handling:');
          print('   - ID: ${kitty.iD} (null expected)');
          print('   - Title: ${kitty.title} (null expected)');
          print('   - Balance: ${kitty.balance} (null expected)');
          print('   - Created At: ${kitty.createdAt} (null expected)');

          expect(kitty.iD, isNull);
          expect(kitty.title, isNull);

        } catch (e) {
          print('⚠️  Kitty model threw exception with invalid data: $e');
        }

        // Test UserKitty model with null data
        try {
          final userKitty = UserKitty.fromJson(null);

          print('✅ UserKitty Model Null Handling:');
          print('   - Kitty Status: ${userKitty.kittyStatus}');
          print('   - Has Membership: ${userKitty.hasMembership}');
          print('   - Percentage: ${userKitty.percentage}');

          expect(userKitty.kittyStatus, equals(''));
          expect(userKitty.hasMembership, isFalse);
          expect(userKitty.percentage, equals(0.0));

        } catch (e) {
          print('❌ UserKitty model failed with null data: $e');
        }

        print('✅ Invalid data handling test completed');
      });

      test('🌐 Test Network Error Scenarios', () async {
        print('\n🔍 Testing Network Error Scenarios...');

        // Test with invalid user data
        final invalidUser = UserModelLatest(phoneNumber: ''); // Empty phone number
        userKittyController.user.value = invalidUser;

        try {
          await userKittyController.getUserkitties(page: 0, size: 10);

          print('📊 Response with invalid user:');
          print('   - Status: ${userKittyController.status.value}');
          print('   - Message: ${userKittyController.apiMessage.value}');
          print('   - Kitties Count: ${userKittyController.kitties.length}');

        } catch (e) {
          print('⚠️  Expected error with invalid user: $e');
        }

        // Test with very large page numbers
        try {
          await userKittyController.getUserkitties(page: 999999, size: 10);

          print('📊 Response with large page number:');
          print('   - Status: ${userKittyController.status.value}');
          print('   - Message: ${userKittyController.apiMessage.value}');

        } catch (e) {
          print('⚠️  Expected error with large page number: $e');
        }

        print('✅ Network error scenarios test completed');
      });
    });

    group('📊 Performance and Load Tests', () {
      test('⚡ Test API Performance', () async {
        print('\n🔍 Testing API Performance...');

        final performanceResults = <String, int>{};

        // Test multiple API calls
        for (int i = 0; i < 3; i++) {
          final stopwatch = Stopwatch()..start();

          try {
            await httpService.request(
              url: ApiUrls.get_kitty,
              method: Method.GET,
            );

            stopwatch.stop();
            performanceResults['call_$i'] = stopwatch.elapsedMilliseconds;

            print('   - API Call $i: ${stopwatch.elapsedMilliseconds}ms');

          } catch (e) {
            stopwatch.stop();
            print('   - API Call $i failed: $e');
          }

          // Small delay between calls
          await Future.delayed(const Duration(milliseconds: 100));
        }

        if (performanceResults.isNotEmpty) {
          final avgTime = performanceResults.values.reduce((a, b) => a + b) / performanceResults.length;
          final maxTime = performanceResults.values.reduce((a, b) => a > b ? a : b);
          final minTime = performanceResults.values.reduce((a, b) => a < b ? a : b);

          print('📊 Performance Summary:');
          print('   - Average Response Time: ${avgTime.toStringAsFixed(1)}ms');
          print('   - Max Response Time: ${maxTime}ms');
          print('   - Min Response Time: ${minTime}ms');

          // Performance assertions
          expect(avgTime, lessThan(5000), reason: 'Average response time should be under 5 seconds');
          expect(maxTime, lessThan(10000), reason: 'Max response time should be under 10 seconds');
        }

        print('✅ Performance test completed');
      });
    });

    group('🎯 Integration Tests', () {
      test('🔗 Test Controller Integration', () async {
        print('\n🔍 Testing Controller Integration...');

        // Test KittyController categories
        try {
          await kittyController.getKittyCategories();

          print('✅ Kitty Categories:');
          print('   - Loading: ${kittyController.isLoadingKittyCategories.value}');
          print('   - Categories Count: ${kittyController.kittyCategories.length}');
          print('   - Filtered Categories: ${kittyController.filteredKittyCategories.length}');

          if (kittyController.kittyCategories.isNotEmpty) {
            final firstCategory = kittyController.kittyCategories.first;
            print('   - First Category: ${firstCategory.toString()}');
          }

        } catch (e) {
          print('⚠️  Kitty categories test failed: $e');
        }

        // Test UserKittyController refresh
        try {
          final testUser = UserModelLatest(phoneNumber: '************');
          userKittyController.user.value = testUser;

          await userKittyController.forceRefresh();

          print('✅ Refresh Kitties:');
          print('   - Use Cache: ${userKittyController.useCache.value}');
          print('   - Current Page: ${userKittyController.currentPage.value}');
          print('   - Kitties Count: ${userKittyController.kitties.length}');

        } catch (e) {
          print('⚠️  Refresh kitties test failed: $e');
        }

        print('✅ Controller integration test completed');
      });
    });
  });
}
