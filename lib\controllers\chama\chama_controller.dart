import 'dart:io';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:mime/mime.dart';
import 'package:onekitty/controllers/events/create_event_controller.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/auth/payments_channels.dart';
import 'package:onekitty/models/chama/chama_contribute_request.dart';
import 'package:onekitty/models/chama/chama_settings.dart';
import 'package:onekitty/models/chama/chama_transactions.dart';
import 'package:onekitty/models/chama/general_penalties_model.dart';
import 'package:onekitty/models/chama/get_text_transaction_request.dart';
import 'package:onekitty/models/chama/signatory_approval.dart';
import 'package:onekitty/models/chama/transfer_req.dart';
import 'package:onekitty/models/events/signatory_transaction_model.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:path/path.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/auth/user_model.dart';
import 'package:onekitty/models/chama/add_signatory_request.dart';
import 'package:onekitty/models/chama/chama_memebers_model.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:onekitty/models/chama/chamaDto.dart';
import 'package:onekitty/models/chama/edit_chama_settings_request.dart';
import 'package:onekitty/models/chama/configs_model.dart';
import 'package:onekitty/models/chama/meetings.dart';
import 'package:onekitty/models/chama/member_penalties.dart';
import 'package:onekitty/models/chama/member_penalty_request.dart';
import 'package:onekitty/models/chama/penalty_model.dart';
import 'package:onekitty/models/chama/signatory_model.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'package:onekitty/models/chama/resource_model.dart';
import 'package:image_picker/image_picker.dart';

class ChamaController extends GetxController implements GetxService {
  final HttpService apiProvider = Get.put(HttpService());
  final dataController = Get.find<ChamaDataController>();
  final box = Get.find<GetStorage>();
  final logger = Get.find<Logger>();
  final ImagePicker _picker = ImagePicker();
  Rx<UserModelLatest> user = UserModelLatest().obs;
  Rx<String> profileUrl = ''.obs;

//Frequency
  RxString apiMessage = ''.obs;
  RxBool getStatus = false.obs;
  RxBool isgetting = false.obs;
  RxList<Frequency> frequencies = <Frequency>[].obs;
  RxList<ChamaRole> roles = <ChamaRole>[].obs;

  RxBool isloading = false.obs;
  RxString whatsappApiMessage = ''.obs;
  RxBool whatsappStatus = false.obs;
  RxMap createRes = {}.obs;

  RxBool isAdding = false.obs;

//Getting Chama Details
  RxString transMessage = ''.obs;
  RxString errorMessage = ''.obs;
  RxBool isloadingChama = false.obs;
  RxBool isChamaLoading = false.obs;
  RxList<ChamaMembers> chamaMembers = <ChamaMembers>[].obs;
  RxList<ChamaMembers> membersOrder = <ChamaMembers>[].obs;
  Rx<OrderData> OData = OrderData().obs;
  RxList<UserChama> chamas = <UserChama>[].obs;
  RxBool isAddPenaltyLoading = false.obs;
  Rx<PenaltyModel> penalty = PenaltyModel().obs;
  RxList<PenaltyModel> penalties = <PenaltyModel>[].obs;
  RxBool isGetChamaPenaltyLoading = false.obs;
  RxBool isSignatoryLoading = false.obs;
  RxBool isGetSignatoryLoading = false.obs;
  RxBool isDeleteSignatoryLoading = false.obs;
  RxBool isDeleteMeetingLoading = false.obs;
  Rx<Datum> signatory = Datum().obs;
  RxList<Datum> signatories = <Datum>[].obs;
  RxBool isAddMeetingLoading = false.obs;
  RxBool isGetMeetingLoading = false.obs;
  RxList<Meeting> meetings = <Meeting>[].obs;
  Rx<Meeting> meeting = Meeting().obs;
  Rx<TransDts?> results = TransDts().obs;
  RxBool status = false.obs;
  RxBool whtsappStatus = false.obs;
  RxString apiMessageProcess = ''.obs;
  RxString beneficiaryName = ''.obs;
  RxString beneficiaryNumber = ''.obs;
  RxString reciveChannel = ''.obs;
  RxString benefAccRef = ''.obs;
  RxString reciveAmount = ''.obs;
  RxMap withdrawData = {}.obs;
  RxBool isPenalizeMemberLoading = false.obs;
  RxBool isGetAllChamaDetailsLoading = false.obs;
  RxBool isGetMemberPenaltiesLoading = false.obs;
  RxList<NotificationCls> notifications = <NotificationCls>[].obs;
  RxBool isSignatory = false.obs;
  RxBool hasSigTrans = false.obs;
  RxInt penaltyCount = 0.obs;
  RxDouble penaltyBal = 0.0.obs;
  RxInt benefPerCycle = 0.obs;
  RxDouble benefPercentage = 0.0.obs;
  RxInt signatureThreshold = 0.obs;
  RxList<Item> memberPenalties = <Item>[].obs;
  RxBool isPenalizeMultiple = false.obs;
  RxBool isEditChamaSettings = false.obs;
  Rx<Settings> setting = Settings().obs;
  RxInt settingId = 0.obs;
  RxBool isTransferReqLoading = false.obs;
  RxDouble amount = 0.0.obs;
  RxDouble charges = 0.0.obs;
  RxDouble thirdPartyCharges = 0.0.obs;
  RxDouble balance = 0.0.obs;
  RxDouble newBalance = 0.0.obs;
  RxInt chamaCount = 0.obs;
  RxDouble penaltyKittyBalance = 0.0.obs;

  RxString accountName = ''.obs;
  RxString receiverAcc = ''.obs;
  RxString receiverAccRef = ''.obs;
  RxList<SignatoryTransaction> sigTransactions = <SignatoryTransaction>[].obs;
  RxBool isGetSigTraLoading = false.obs;
  RxBool isSignatoryApproveLoading = false.obs;
  RxBool isChamaContributionLoading = false.obs;
  RxString chamaTitle = ''.obs;
  RxString memberNames = ''.obs;
  RxString ctrAccNo = ''.obs;
  RxString penaltyName = ''.obs;
  RxInt ctrAmt = 0.obs;
  RxList<GeneralPenalty> generalPenalties = <GeneralPenalty>[].obs;
  RxBool isGetGeneralPenalties = false.obs;
  RxBool isDeletePenaltyLoading = false.obs;
  Rx<ChamaSetting> chamaSettings = ChamaSetting().obs;
  var checkboxStates = <bool>[].obs;
  RxList<TransactionModel> chamaTransactions = <TransactionModel>[].obs;
  RxBool isGetChamaTransactionLoading = false.obs;
  RxBool isPostTextTransactionLoading = false.obs;
  RxBool isGetMemberPenUsingAccNo = false.obs;
  RxList<MemPenalty> memPenaltiesUsingAcc = <MemPenalty>[].obs;
  RxBool hasPenalties = false.obs;
  RxString accountNo = ''.obs;
  RxInt chamaId = 0.obs;
  RxMap contributeData = {}.obs;

  RxString textmessage = ''.obs;

  RxBool loadingMore = false.obs;

  //set_order
  RxBool isSetting = false.obs;

  //resources
  RxBool isUploading = false.obs;
  RxBool isgetRes = false.obs;
  RxList<ResourceDts> resources = <ResourceDts>[].obs;

  //update chama
  RxBool isUpdating = false.obs;
  Rx<Chama> chamaDetails = Chama().obs;
  RxBool isAddingG = false.obs;
  RxBool isUpdatingM = false.obs;

  //beneficiaries
  RxList<NextBeneficiary> beneficiaries = <NextBeneficiary>[].obs;

  int size = 20;
  int page = 0;
  int perpage = 10;
  RxBool scrollEnd = false.obs;
//media
  final isUploadingImage = false.obs;
  Future<bool?> pickImage(
      {required int kittyId,
      required String name,
      required BuildContext context,
      bool isUpdating = false}) async {
    isUploadingImage(true);
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 2048,
        maxHeight: 2048,
      );

      if (image != null) {
        final _eventsController = Get.find<CreateEventController>();
        final file = image.path;
        final fileSize = await File(file).length();
        final fileSizeInMB = fileSize / (1024 * 1024);

        final String mimeType = lookupMimeType(file) ?? '';
        if (!mimeType.startsWith('image/')) {
          ToastUtils.showToast('Please select an image file');
          isUploadingImage(false);
          return false;
        }

        if (fileSizeInMB <= 20) {
          final url = await _eventsController.uploadFile(
              path: file,
              fileName:
                  "${DateTime.now().millisecondsSinceEpoch}${file.split(RegExp(r'[/\\]')).last}");
          var resp = await apiProvider.request(
              url: ApiUrls.update_chama,
              method: Method.POST,
              params: UpdateDto(
                id: dataController.singleChamaDts.value.id ?? 1,
                title: dataController.singleChamaDts.value.title ?? '',
                description:
                    dataController.singleChamaDts.value.description ?? '',
                email: dataController.singleChamaDts.value.email ?? '',
                frequency: dataController.singleChamaDts.value.frequency ?? '',
                amount: int.parse(
                    dataController.singleChamaDts.value.amount.toString()),
                nextOccurrence:
                    dataController.singleChamaDts.value.nextOccurrence ??
                        DateTime(1000),
                profile: url,
              ).toJson());

          if (resp.data['status'] == true) {
            dataController.singleChamaDts.value = Chama(
              id: dataController.singleChamaDts.value.id,
              title: dataController.singleChamaDts.value.title,
              description: dataController.singleChamaDts.value.description,
              email: dataController.singleChamaDts.value.email,
              frequency: dataController.singleChamaDts.value.frequency,
              amount: dataController.singleChamaDts.value.amount,
              nextOccurrence:
                  dataController.singleChamaDts.value.nextOccurrence,
              profileUrl: url,
            );
            chamaDetails.value = Chama(
              id: chamaDetails.value.id,
              title: chamaDetails.value.title,
              description: chamaDetails.value.description,
              email: chamaDetails.value.email,
              frequency: chamaDetails.value.frequency,
              amount: chamaDetails.value.amount,
              nextOccurrence: chamaDetails.value.nextOccurrence,
              profileUrl: url,
            );
            profileUrl.value = url ?? '';
            dataController.update();
            update();

            apiMessage(resp.data["message"]);
            return true;
          } else {
            ToastUtils.showToast(resp.data["message"] ?? 'Upload failed');
            return false;
          }
        } else {
          ToastUtils.showToast(
              'Image size must be 20MB or less (Current size: ${fileSizeInMB.toStringAsFixed(1)}MB)');
          return false;
        }
      } else {
        ToastUtils.showToast('No image selected');
        return false;
      }
    } catch (e) {
      logger.e('General error in pickImage: $e');
      ToastUtils.showToast('Failed to process image. Please try again.');
      return false;
    } finally {
      isUploadingImage(false);
    }
  }

  RxBool isPaginating = false.obs;

  // Pagination variables
  int currentPage = 0;
  int totalPages = 1;
  bool hasMoreData = true;

  // Scroll controllers
  final ScrollController pendingScrollController = ScrollController();
  final ScrollController processedScrollController = ScrollController();
  // Assume these are declared in your controller or here:

  @override
  void onInit() {
    getLocalUser();
    controller.addListener(_scrollListener);
    chcontroller.addListener(_scrollListeners);

    _setupScrollListeners();

    super.onInit();
  }

  @override
  void dispose() {
    size = 10;
    page = 0;
    perpage = 10;
    scrollEnd.value = false;
    profileUrl.value = '';
    controller.removeListener(_scrollListener);
    chcontroller.removeListener(_scrollListeners);
    chcontroller.dispose();
    super.dispose();
  }

  void reset() {
    size = 20;
    perpage = 10;
    scrollEnd = false.obs;
  }

  ScrollController chcontroller = ScrollController();

  void _scrollListeners() async {
    loadingMore(true);

    if (chcontroller.position.atEdge && chcontroller.position.pixels != 0) {
      await loadMoreChamas();
    }
    loadingMore(false);
  }

  ScrollController controller = ScrollController();
  void _scrollListener() async {
    loadingMore(true);
    if (controller.position.atEdge && controller.position.pixels != 0) {
      await loadMoreMembers(
          chamaId: dataController.singleChamaDts.value.id ?? 0,
          sort: "LEADERS");
    }
    loadingMore(false);
  }

  UserModelLatest? getLocalUser() {
    final usr = box.read(CacheKeys.user);
    if (usr != null) {
      user(UserModelLatest.fromJson(usr));
      return user.value;
    } else {
      return null;
    }
  }

  Future<bool> getConfigs() async {
    isgetting(true);
    try {
      var res = await apiProvider.request(
        url: ApiUrls.getFreq,
        method: Method.GET,
      );
      apiMessage(res.data["message"]);
      getStatus(res.data["status"]);
      if (res.data['status']) {
        frequencies([]);
        for (var element in res.data["data"]["frequencies"]) {
          frequencies.add(Frequency.fromJson(element));
        }
        roles([]);
        for (var role in res.data["data"]["chama_roles"]) {
          roles.add(ChamaRole.fromJson(role));
        }
        update();
        isgetting(false);
        return true;
      } else {
        isgetting(false);

        return false;
      }
    } catch (e) {
      logger.e(e);
      isgetting(false);

      return false;
    }
  }

  Future<bool> getChamaDetails({required chamaId}) async {
    try {
      var res = await apiProvider.request(
        url: "${ApiUrls.get_all}$chamaId",
        method: Method.GET,
      );

      if (res.data["status"]) {
        chamaDetails(Chama.fromJson(res.data["data"]["chama"]));
        profileUrl.value = chamaDetails.value.profileUrl ?? '';
        return true;
      } else {
        apiMessage(res.data["message"]);
        return false;
      }
    } catch (e) {
      apiMessage(e.toString());
      return false;
    }
  }

  Future<bool> createChama({required CreateDto createDto}) async {
    isloading(true);
    try {
      var resp = await apiProvider.request(
        url: ApiUrls.create_chama,
        method: Method.POST,
        params: createDto.toJson(),
      );

      if (resp.data["status"]) {
        apiMessage(resp.data["message"]);
        createRes(resp.data["chama"]);
        whatsappApiMessage(resp.data["whatsapp_message"]);
        whatsappStatus(resp.data["whatsapp_status"]);
        isloading(false);

        return true;
      } else {
        apiMessage(resp.data["message"]);
        isloading(false);
        return false;
      }
    } catch (e) {
      logger.e(e);
      isloading(false);
      apiMessage('An error occured');
      return false;
    }
  }

  Future<bool> addMember({required MembersDto memebersDto}) async {
    isAdding(true);
    try {
      var resp = await apiProvider.request(
        url: ApiUrls.add_members,
        method: Method.POST,
        params: memebersDto.toJson(),
      );

      if (resp.data["status"]) {
        apiMessage(resp.data["message"]);
        isAdding(false);

        return true;
      } else {
        apiMessage(resp.data["message"]);
        isAdding(false);
        return false;
      }
    } catch (e) {
      logger.e(e);
      isAdding(false);
      apiMessage('An error occured');
      return false;
    }
  }

  Future<void> loadMoreChamas() async {
    loadingMore(true);
    if (perpage < chamaCount.value) {
      perpage =
          (perpage + 5 > chamaCount.value) ? chamaCount.value : perpage + 5;
      await getUserChamas(
        size: perpage,
      );
      if (perpage == chamaCount.value) {
        scrollEnd.value = true;
      }
    }
  }

  Future<void> getUserChamas({int? page = 0, int? size = 10}) async {
    update();

    try {
      var resp = await apiProvider.request(
          url:
              "${ApiUrls.getUserChamas}?phone_number=${getLocalUser()?.phoneNumber}&page=$page&size=$size",
          method: Method.GET);
      status(resp.data["status"]);
      apiMessage(resp.data["message"]);
      if (resp.data["status"]) {
        chamas([]);
        for (var element in resp.data["data"]["chamas"] ?? []) {
          chamas.add(UserChama.fromJson(element));
        }
        chamaCount(resp.data["data"]["count"]);
        logger.log(Level.debug, resp.data);
      }
      update();
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured');
      update();
      throw e;
    }
  }

  Future<void> getChamaMembers(
      {int? chamaId, int? page = 0, int? size = 20, String? sort}) async {
    update();

    try {
      var resp = await apiProvider.request(
          url:
              "${ApiUrls.getChamaMembers}?chama_id=$chamaId&page=$page&size=$size&sort-by=$sort",
          method: Method.GET);
      status(resp.data["status"]);
      apiMessage(resp.data["message"]);
      if (resp.data["status"]) {
        OData.value = OrderData.fromJson(resp.data["data"]);
        chamaMembers([]);
        for (var element in resp.data["data"]["items"] ?? []) {
          chamaMembers.add(ChamaMembers.fromJson(element));
        }
      }
      checkboxStates.value = List<bool>.filled(chamaMembers.length, false);
      isloadingChama(false);
      update();
    } catch (e) {
      isloadingChama(false);
      logger.e(e);
      apiMessage('An error occured');
      update();

      throw e;
    }
  }

  // Future<void> LoadMembers({int? chamaId, sort}) async {
  //  if (size < OData.value.total!) {
  //     size = (size + 2 > OData.value.total!) ? OData.value.total! : size + 2;
  //     await getChamaMembers(
  //         chamaId: chamaId, page: page, size: size, sort: sort);
  //     if (size == OData.value.total!) {
  //       scrollEnd.value = true;
  //     }
  //   }
  // }

  Future<void> loadMoreMembers({int? chamaId, sort}) async {
    if (size < OData.value.total!) {
      size = (size + 20 > OData.value.total!) ? OData.value.total! : size + 20;
      await getChamaMembers(
          chamaId: chamaId, page: page, size: size, sort: sort);
      if (size == OData.value.total!) {
        scrollEnd.value = true;
      }
    }
  }

  Future<void> getMembersOrder(
      {int? chamaId, int? page = 0, int? size, String? sort}) async {
    isloadingChama(true);
    update();

    try {
      var resp = await apiProvider.request(
          url:
              "${ApiUrls.getChamaMembers}?chama_id=$chamaId&page=$page&size=$size&sort-by=$sort",
          method: Method.GET);
      status(resp.data["status"]);
      apiMessage(resp.data["message"]);
      if (resp.data["status"]) {
        OData.value = OrderData.fromJson(resp.data["data"]);
        membersOrder([]);
        for (var element in resp.data["data"]["items"] ?? []) {
          membersOrder.add(ChamaMembers.fromJson(element));
        }
      }
      checkboxStates.value = List<bool>.filled(chamaMembers.length, false);
      isloadingChama(false);
      update();
    } catch (e) {
      isloadingChama(false);
      logger.e(e);
      apiMessage('An error occured');
      update();

      throw e;
    }
  }

  List<ChamaMembers> getSelectedMembers() {
    List<ChamaMembers> selectedMembers = [];
    for (int i = 0; i < chamaMembers.length; i++) {
      if (checkboxStates[i]) {
        selectedMembers.add(chamaMembers[i]);
      }
    }
    return selectedMembers;
  }

  Future<bool> addPenalty(
      {required dynamic request, bool isUpdate = false}) async {
    isAddPenaltyLoading(true);
    try {
      String url = isUpdate ? ApiUrls.updatePenalty : ApiUrls.addPenalty;
      Method method = isUpdate ? Method.PUT : Method.POST;
      var res = await apiProvider.request(
          url: url, method: method, params: request.toJson());
      apiMessage(res.data["message"]);
      logger.log(Level.debug, res.data);
      if (res.data["status"]) {
        penalty(PenaltyModel.fromJson(res.data["data"]));
      }
      isAddPenaltyLoading(false);
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      isAddPenaltyLoading(false);
      apiMessage('An error occured');
      return false;
    }
  }

  Future<bool> deletePenalty(
      {required int chamaId, required int penaltyId}) async {
    isDeletePenaltyLoading(true);
    try {
      var res = await apiProvider.request(
        url: '${ApiUrls.deletePenalty}?chama_id=$chamaId&penalty_id=$penaltyId',
        method: Method.DELETE,
      );
      apiMessage(res.data["message"]);
      if (res.data["status"]) {
        apiMessage(res.data["message"]);
      }
      isDeletePenaltyLoading(false);
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      isDeletePenaltyLoading(false);
      apiMessage('An error occurred');
      return false;
    }
  }

  Future<void> getChamaPenalties({required int chamaId}) async {
    isGetChamaPenaltyLoading(true);
    try {
      var resp = await apiProvider.request(
          url: "${ApiUrls.getChamaPenalties}?chama_id=$chamaId",
          method: Method.GET);
      apiMessage(resp.data["message"]);
      logger.log(Level.debug, resp.data);
      if (resp.data["status"]) {
        penalties([]);
        for (var element in resp.data["data"] ?? []) {
          penalties.add(PenaltyModel.fromJson(element));
        }
      }
      isGetChamaPenaltyLoading(false);
      update();
    } catch (e) {
      isGetChamaPenaltyLoading(false);
      logger.e(e);
      apiMessage('An Error occurred');
      update();
    }
  }

  Future<bool> setReceivingOrder({required SetOrderDto setOrderDto}) async {
    isSetting(true);
    try {
      var res = await apiProvider.request(
        url: ApiUrls.set_order,
        method: Method.POST,
        params: setOrderDto.toJson(),
      );
      if (res.data["status"]) {
        apiMessage(res.data["message"]);
        isSetting(false);

        return true;
      } else {
        apiMessage(res.data["message"]);
        isSetting(false);

        return false;
      }
    } catch (e) {
      apiMessage(e.toString());
      isSetting(false);
      return false;
    }
  }

  Future<bool> addSignatory({required SignatoryRequest request}) async {
    isSignatoryLoading(true);
    try {
      var res = await apiProvider.request(
          url: ApiUrls.addSignatory,
          method: Method.POST,
          params: request.toJson());
      apiMessage(res.data["message"]);
      if (res.data["status"]) {
        apiMessage(res.data["message"]);
      }
      isSignatoryLoading(false);
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      isSignatoryLoading(false);
      return false;
    }
  }

  Future<String> uploadMedia(File file) async {
    isUploading(true);
    final Reference storageReference = FirebaseStorage.instance
        .ref()
        .child('resources/${basename(file.path)}');

    UploadTask uploadTask = storageReference.putFile(file);
    String downloadURL = await (await uploadTask).ref.getDownloadURL();
    isUploading(false);
    return downloadURL;
  }

  Future<bool> uploadResource({required UploadRDto uploadDto}) async {
    isUploading(true);
    try {
      var resp = await apiProvider.request(
        url: ApiUrls.add_resource,
        method: Method.POST,
        params: uploadDto.toJson(),
      );

      if (resp.data["status"]) {
        apiMessage(resp.data["message"]);
        isUploading(false);

        return true;
      } else {
        apiMessage(resp.data["message"]);
        isUploading(false);
        return false;
      }
    } catch (e) {
      logger.e(e);
      isUploading(false);
      apiMessage('An error occured');
      return false;
    }
  }

  Future<bool> updateResource({required UploadRDto uploadDto}) async {
    isUploading(true);
    try {
      var resp = await apiProvider.request(
        url: ApiUrls.update_resource,
        method: Method.PUT,
        params: uploadDto.toJson(),
      );

      if (resp.data["status"]) {
        apiMessage(resp.data["message"]);
        isUploading(false);

        return true;
      } else {
        apiMessage(resp.data["message"]);
        isUploading(false);
        return false;
      }
    } catch (e) {
      logger.e(e);
      isUploading(false);
      apiMessage('An error occured');
      return false;
    }
  }

  Future<void> getSignatories({required int chamaId}) async {
    isGetSignatoryLoading(true);
    update();
    try {
      var res = await apiProvider.request(
          url: "${ApiUrls.getSignatories}?chama_id=$chamaId",
          method: Method.GET);
      apiMessage(res.data["message"]);
      logger.log(Level.debug, res.data);
      if (res.data["status"]) {
        signatories([]);
        for (var element in res.data["data"] ?? []) {
          signatories.add(Datum.fromJson(element));
        }
      }
      isGetSignatoryLoading(false);
      update();
    } catch (e) {
      isGetSignatoryLoading(false);
      logger.e(e);
      apiMessage('An Error occurred');
      update();
    }
  }

  Future<bool> deleteSignatory(
      {required int signatoryId, required int chamaId}) async {
    isDeleteSignatoryLoading(true);
    try {
      var res = await apiProvider.request(
        url:
            '${ApiUrls.getSignatories}?chama_id=$chamaId&signatory_id=$signatoryId', // Assuming deleteSignatory API requires signatoryId in the URL
        method: Method.DELETE,
      );
      apiMessage(res.data["message"]);
      if (res.data["status"]) {
        apiMessage(res.data["message"]);
      }
      isDeleteSignatoryLoading(false);
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      isDeleteSignatoryLoading(false);
      apiMessage('An error occurred');
      return false;
    }
  }

  Future<bool> addMeeting(
      {required dynamic request, bool isUpdate = false}) async {
    isAddMeetingLoading(true);
    try {
      String url = isUpdate ? ApiUrls.updateMeeting : ApiUrls.addMeeting;
      Method method = isUpdate ? Method.PUT : Method.POST;
      var res = await apiProvider.request(
          url: url, method: method, params: request.toJson());
      apiMessage(res.data["message"]);
      if (res.data["status"]) {
        apiMessage(res.data["message"]);
      }
      isAddMeetingLoading(false);
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      isAddMeetingLoading(false);
      apiMessage("An error occured");
      return false;
    }
  }

  Future<void> getChamaMeetings({required int chamaId}) async {
    isGetMeetingLoading(true);
    update();
    try {
      var res = await apiProvider.request(
          url: "${ApiUrls.getMeeting}?chama_id=$chamaId", method: Method.GET);
      apiMessage(res.data["message"]);
      if (res.data["status"]) {
        meetings([]);
        for (var element in res.data["data"] ?? []) {
          meetings.add(Meeting.fromJson(element));
        }
      }
      isGetMeetingLoading(false);
      update();
    } catch (e) {
      isGetMeetingLoading(false);
      logger.e(e);
      apiMessage("An error occured");
      update();
    }
  }

  Future<bool> deleteMeeting(
      {required int meetingId, required int chamaId}) async {
    isDeleteMeetingLoading(true);
    try {
      var res = await apiProvider.request(
        url: '${ApiUrls.addMeeting}?chama_id=$chamaId&event_id=$meetingId',
        method: Method.DELETE,
      );
      apiMessage(res.data["message"]);
      if (res.data["status"]) {
        apiMessage(res.data["message"]);
      }
      isDeleteMeetingLoading(false);
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      isDeleteMeetingLoading(false);
      apiMessage('An error occurred');
      return false;
    }
  }

  Future<bool> penalizeMember({required PenalizeMemberRequest request}) async {
    isPenalizeMemberLoading(true);
    try {
      var res = await apiProvider.request(
          url: ApiUrls.penalizeMember,
          method: Method.POST,
          params: request.toJson());
      if (res.data["status"]) {
        apiMessage(res.data["message"]);
      }
      isPenalizeMemberLoading(false);
      return res.data["status"];
    } catch (e) {
      isPenalizeMemberLoading(false);
      logger.e(e);
      apiMessage("An error occured");
      return false;
    }
  }

  Future<void> getGeneralPenalties({required int chamaId}) async {
    isGetGeneralPenalties(true);
    update();
    try {
      var res = await apiProvider.request(
          url: "${ApiUrls.getGeneralPenalties}?chama_id=$chamaId",
          method: Method.GET);
      apiMessage(res.data["message"]);
      if (res.data["status"]) {
        generalPenalties([]);
        for (var element in res.data["data"]) {
          generalPenalties.add(GeneralPenalty.fromJson(element));
        }
      }
      isGetGeneralPenalties(false);
      update();
    } catch (e) {
      isGetGeneralPenalties(false);
      logger.e(e);
      apiMessage('An Error occurred');
      update();
    }
  }

  Future<void> getMemberPenalties({required int chamaId, required int memeberId}) async {
    isGetMemberPenaltiesLoading(true);
    update();
    try {
      var res = await apiProvider.request(
          url:
              "${ApiUrls.penalizeMember}?member_id=$memeberId&chama_id=$chamaId",
          method: Method.GET);
      apiMessage(res.data["message"]);
      if (res.data["status"]) {
        memberPenalties([]);
        for (var element in res.data["data"]["items"]) {
          memberPenalties.add(Item.fromJson(element));
        }
      }
      isGetMemberPenaltiesLoading(false);
      update();
    } catch (e) {
      isGetMemberPenaltiesLoading(false);
      logger.e(e);
      apiMessage('An Error occurred');
      update();
    }
  }

  Future<void> getAllChamaDetails({required int chamaId}) async {
    isGetAllChamaDetailsLoading(true);
    update();
    try {
      var res = await apiProvider.request(
          url: ApiUrls.getAllChamaDetails + chamaId.toString(),
          method: Method.GET);
      apiMessage(res.data["message"]);
      isGetAllChamaDetailsLoading(false);
      logger.log(Level.debug, res.data);
      if (res.data["status"]) {
        isSignatory(res.data["data"]["is_signatory"]);
        hasSigTrans(res.data["data"]["has_signatory_trans"]);
        penaltyCount(res.data["data"]["penalties_count"]);
        penaltyKittyBalance(double.tryParse(
                res.data["data"]["penalty_kitty_balance"].toString()) ??
            0.0);
        beneficiaries([]);
        notifications([]);
        for (var element in res.data["data"]["next_beneficiaries"] ?? []) {
          beneficiaries.add(NextBeneficiary.fromJson(element));
        }

        for (var element in res.data["data"]["notification"]) {
          notifications.add(NotificationCls.fromJson(element));
        }
        chamaDetails(Chama.fromJson(res.data["data"]["chama"]));
        profileUrl.value = chamaDetails.value.profileUrl ?? '';
        penaltyBal(double.tryParse(
            "${res.data["data"]["penalty_kitty_balance"] ?? 0.0}"));
      }
      isGetAllChamaDetailsLoading(false);
      update();
    } catch (e) {
      isGetAllChamaDetailsLoading(false);
      logger.e(e);
      apiMessage("An error occured");
      update();
    }
  }

  Future<void> getAllChamaDetailsWithKittyId({required int kittyId}) async {
    isGetAllChamaDetailsLoading(true);
    update();
    try {
      var res = await apiProvider.request(
          url: ApiUrls.getKittyChamaDetails + kittyId.toString(),
          method: Method.GET);
      apiMessage(res.data["message"]);
      isGetAllChamaDetailsLoading(false);
      logger.log(Level.debug, res.data);
      if (res.data["status"]) {
        isSignatory(res.data["data"]["is_signatory"]);
        hasSigTrans(res.data["data"]["has_signatory_trans"]);
        penaltyCount(res.data["data"]["penalties_count"]);
        penaltyKittyBalance(double.tryParse(
                res.data["data"]["penalty_kitty_balance"].toString()) ??
            0.0);
        beneficiaries([]);
        notifications([]);
        for (var element in res.data["data"]["next_beneficiaries"] ?? []) {
          beneficiaries.add(NextBeneficiary.fromJson(element));
        }

        for (var element in res.data["data"]["notification"]) {
          notifications.add(NotificationCls.fromJson(element));
        }
        chamaDetails(Chama.fromJson(res.data["data"]["chama"]));
        profileUrl.value = chamaDetails.value.profileUrl ?? '';
        penaltyBal(double.tryParse(
            "${res.data["data"]["penalty_kitty_balance"] ?? 0.0}"));
      }
      isGetAllChamaDetailsLoading(false);
      update();
    } catch (e) {
      isGetAllChamaDetailsLoading(false);
      logger.e(e);
      apiMessage("An error occured");
      update();
    }
  }

  Future<bool> penalizeMultiple(
      {required MultiplePenaltyRequest request}) async {
    isPenalizeMultiple(true);
    try {
      var res = await apiProvider.request(
          url: ApiUrls.penalizeMultiple,
          method: Method.POST,
          params: request.toJson());
      apiMessage(res.data["message"]);
      if (res.data["status"]) {
        apiMessage(res.data["message"]);
      }
      isPenalizeMultiple(false);
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      isPenalizeMultiple(false);
      apiMessage('An error occured');
      return false;
    }
  }

  Future<bool> editChamaSettings(
      {required EditChamaSettingsRequest request}) async {
    isEditChamaSettings(true);
    try {
      var res = await apiProvider.request(
          url: ApiUrls.editChamaSettings,
          method: Method.PUT,
          params: request.toJson());
      apiMessage(res.data["message"]);
      if (res.data["status"]) {
        setting(Settings.fromJson(res.data["data"]));
      }
      isEditChamaSettings(false);
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      isEditChamaSettings(false);
      apiMessage("An error occured");
      return false;
    }
  }

  Future<void> getResources({required chamaId}) async {
    isgetRes(true);
    update();
    try {
      var res = await apiProvider.request(
        url: "${ApiUrls.getResources}?chama_id=$chamaId",
        method: Method.GET,
      );
      if (res.data["status"]) {
        resources([]);
        for (var item in res.data["data"]) {
          resources.add(ResourceDts.fromJson(item));
        }
      }
      isgetRes(false);
    } catch (e) {
      isgetRes(false);

      apiMessage(e.toString());
    }
  }

  Future<bool> UpdateChama({required UpdateDto updateDto}) async {
    isUpdating(true);
    try {
      var resp = await apiProvider.request(
        url: ApiUrls.update_chama,
        method: Method.POST,
        params: updateDto.toJson(),
      );

      if (resp.data["status"]) {
        apiMessage(resp.data["message"]);
        isUpdating(false);

        return true;
      } else {
        apiMessage(resp.data["message"]);
        isUpdating(false);

        return false;
      }
    } catch (e) {
      logger.e(e);
      isUpdating(false);
      apiMessage('An error occured');
      return false;
    }
  }

  Future<bool> updateRole(
      {String? notificationType,
      String? email,
      String? whatsApp,
      required int chId,
      required int mId,
      required String role,
      required bool isSignatory}) async {
    isUpdatingM(true);
    try {
      var res = await apiProvider.request(
        url: ApiUrls.update_role,
        method: Method.POST,
        params: {
          "email": email,
          "whatsApp": whatsApp,
          "notificationType": notificationType,
          "chama_id": chId,
          "member_id": mId,
          "role": role,
          "is_signatory": isSignatory,
        },
      );

      if (res.data["status"]) {
        apiMessage(res.data["message"]);
        isUpdatingM(false);

        return true;
      } else {
        apiMessage(res.data["message"]);
        isUpdatingM(false);

        return false;
      }
    } catch (e) {
      apiMessage(e.toString());
      isUpdatingM(false);

      return false;
    }
  }

  Future<bool> RmWhatsapp({
    required int chId,
    required int nId,
  }) async {
    isloading(true);
    try {
      var res = await apiProvider.request(
        url: '${ApiUrls.rm_whatsApp}?chama_id=$chId&notification_id=$nId',
        method: Method.DELETE,
      );

      apiMessage(res.data["message"]);
      if (res.data["status"]) {
        isloading(false);
        // toggleData(res.data["data"]);
        return true;
      } else {
        isloading(false);
        apiMessage(res.data["message"]);
        return false;
      }
    } catch (e) {
      isloading(false);
      logger.e(e);
      apiMessage("An error occured");
      return false;
    }
  }

  Future<bool> toggleWhatsapp({
    required int nId,
    required String status,
  }) async {
    isloading(true);
    try {
      var res = await apiProvider
          .request(url: ApiUrls.toggle_whatsApp, method: Method.PUT, params: {
        "id": nId,
        "whatsapp_status": status,
      });

      apiMessage(res.data["message"]);
      if (res.data["status"]) {
        isloading(false);
        // toggleData(res.data["data"]);
        return true;
      } else {
        isloading(false);
        apiMessage(res.data["message"]);
        return false;
      }
    } catch (e) {
      isloading(false);
      logger.e(e);
      apiMessage("An error occured");
      return false;
    }
  }

  Future<bool> addGroup(
      {required int chId, required String link, required String email}) async {
    isAddingG(true);
    update();
    try {
      var res = await apiProvider.request(
        url: ApiUrls.add_group,
        method: Method.POST,
        params: {"chama_id": chId, "whats_app_link": link, "email": email},
      );

      if (res.data["status"]) {
        apiMessage(res.data["message"]);
        isAddingG(false);
        return true;
      } else {
        apiMessage(res.data["message"]);
        isAddingG(false);
        return false;
      }
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured${e.toString()}');
      isAddingG(false);
      return false;
    }
  }

  Future<bool> transferReq(
      {required TransferRequest request, required bool isConfirm}) async {
    isTransferReqLoading(true);
    try {
      var res = await apiProvider.request(
          url: isConfirm ? ApiUrls.transferConfirm : ApiUrls.transferReq,
          method: Method.POST,
          params: request.toJson());
      apiMessage(res.data["message"]);
      logger.log(Level.debug, res.data);
      status(res.data["status"]);
      if (res.data["status"] && !isConfirm) {
        var data = res.data["data"];
        amount(double.tryParse(data["amount"]?.toString() ?? '') ?? 0.0);
        charges(
            double.tryParse(data["charges_total"]?.toString() ?? '') ?? 0.0);
        thirdPartyCharges(
            double.tryParse(data["third_party_charges"]?.toString() ?? '') ?? 0.0);
        balance(double.tryParse(data["balance"]?.toString() ?? '') ?? 0.0);
        newBalance(double.tryParse(data["balance_new"]?.toString() ?? '') ?? 0.0);
        accountName(data["account_name"] ?? '');
        receiverAcc(data["receiver_account"] ?? '');
        receiverAccRef(data["receiver_account_ref"] ?? '');
      }
      isTransferReqLoading(false);
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      isTransferReqLoading(false);
      return false;
    }
  }

  void _setupScrollListeners() {
    final ChamaDataController chamaDataController =
        Get.put(ChamaDataController());
    void onScroll(ScrollController controller) {
      if (controller.position.pixels >=
              controller.position.maxScrollExtent - 100 &&
          !isPaginating.value &&
          hasMoreData) {
        final chamaId = chamaDataController.chama.value.chama?.id ?? 0;
        getSigTransactions(chamaId: chamaId, page: currentPage + 1);
      }
    }

    pendingScrollController
        .addListener(() => onScroll(pendingScrollController));
    processedScrollController
        .addListener(() => onScroll(processedScrollController));
  }

  Future<void> getSigTransactions({required int chamaId, int page = 0}) async {
    // If it's the first page, clear existing data

    isGetSigTraLoading(true);
    if (page == 0) {
      sigTransactions.clear();
      currentPage = 0;
      hasMoreData = true;
      isGetSigTraLoading(true);
    } else {
      // If loading more data, show pagination loading
      isPaginating(true);
    }

    update();

    try {
      var res = await apiProvider.request(
        url: "${ApiUrls.getSigTra}?chama_id=$chamaId&page=$page",
        method: Method.GET,
      );

      if (res.data["status"]) {
        // Update pagination info
        // Print API response data for debugging
        logger.d('Page: ${res.data["data"]["page"]}');
        logger.d('Total Pages: ${res.data["data"]["total_pages"]}');
        logger.d('Is Last Page: ${res.data["data"]["last"]}');
        logger.d(
            'Items Count: ${(res.data["data"]["items"] as List?)?.length ?? 0}');
        currentPage = res.data["data"]["page"] ?? page;
        totalPages = res.data["data"]["total_pages"] ?? 1;
        hasMoreData = !(res.data["data"]["last"] ?? true);

        // Add new items to the list
        final newItems = (res.data["data"]["items"] as List? ?? [])
            .map((e) => SignatoryTransaction.fromJson(e))
            .toList();

        if (page == 0) {
          sigTransactions.assignAll(newItems);
        } else {
          sigTransactions.addAll(newItems);
        }
      }

      apiMessage(res.data["message"]);
    } catch (e) {
      logger.e(e);
      apiMessage('An Error occurred');
    } finally {
      isGetSigTraLoading(false);
      isPaginating(false);
      update();
    }
  }

  Future<bool> signatoryApproval(
      {required SignatoryApprovalModel request}) async {
    isSignatoryApproveLoading(true);
    try {
      var res = await apiProvider.request(
          url: ApiUrls.sigApproval,
          method: Method.POST,
          params: request.toJson());
      apiMessage(res.data["message"]);
      logger.log(Level.debug, res.data);
      if (res.data["status"] ?? false) {
        apiMessage(res.data["message"] ?? 'error');
      }
      isSignatoryApproveLoading(false);
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      isSignatoryApproveLoading(false);
      apiMessage('An error occured');
      return false;
    }
  }

  Future getChamaSettings({required int chamaId}) async {
    isGetAllChamaDetailsLoading(true);
    update();
    try {
      var res = await apiProvider.request(
          url: "${ApiUrls.getChamaSettings}?chama_id=$chamaId",
          method: Method.GET);
      final data = res.data["data"];
      logger.log(Level.debug, res.data);
      if (data != null) {
        final settingss = ChamaSetting.fromJson(data);
        chamaSettings(settingss);
        benefPerCycle(settingss.beneficiariesPerCycle);
        benefPercentage(settingss.beneficiaryPercentage);
        signatureThreshold(settingss.signatureThreshold);
        settingId(settingss.id);
      } else {
        apiMessage(res.data["message"]);
      }
      status(res.data["status"]);
      isGetAllChamaDetailsLoading(false);
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      apiMessage("Error,Please try again");
      isGetAllChamaDetailsLoading(false);
      return false;
    }
  }

  Future<bool> updateBenfAcc({required UpdateBenf updtDto}) async {
    isUpdatingM(true);
    update();
    try {
      var res = await apiProvider.request(
        url: ApiUrls.update_benf_acc,
        method: Method.PUT,
        params: updtDto.toJson(),
      );

      if (res.data["status"]) {
        apiMessage(res.data["message"]);
        isUpdatingM(false);

        return true;
      } else {
        apiMessage(res.data["message"]);
        isUpdatingM(false);
        return false;
      }
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured${e.toString()}');
      isUpdatingM(false);
      return false;
    }
  }

  Future<bool> RmResource({
    required int chId,
    required int rsId,
  }) async {
    isUpdating(true);
    update();
    try {
      var res = await apiProvider.request(
        url: '${ApiUrls.rm_resource}?chama_id=$chId&media_id=$rsId',
        method: Method.DELETE,
      );

      if (res.data["status"]) {
        apiMessage(res.data["message"]);
        isUpdating(false);
        return true;
      } else {
        apiMessage(res.data["message"]);
        isUpdating(false);
        return false;
      }
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured${e.toString()}');
      isUpdating(false);
      return false;
    }
  }

  Future<bool> RmMember(
      {required int chId, required int mId, required String status}) async {
    isUpdatingM(true);
    update();
    try {
      var res = await apiProvider.request(
        url: ApiUrls.removeM,
        method: Method.POST,
        params: {"chama_id": chId, "member_id": mId, "status": status},
      );

      if (res.data["status"]) {
        apiMessage(res.data["message"]);
        isUpdatingM(false);

        return true;
      } else {
        apiMessage(res.data["message"]);
        isUpdatingM(false);
        return false;
      }
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured${e.toString()}');
      isUpdatingM(false);
      return false;
    }
  }

  Future<bool> updateMember({required UpdtMemDto updtDto}) async {
    isUpdatingM(true);
    update();
    try {
      var res = await apiProvider.request(
        url: ApiUrls.update_member,
        method: Method.POST,
        params: updtDto.toJson(),
      );

      if (res.data["status"]) {
        apiMessage(res.data["message"]);
        isUpdatingM(false);

        return true;
      } else {
        apiMessage(res.data["message"]);
        isUpdatingM(false);
        return false;
      }
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured${e.toString()}');
      isUpdatingM(false);
      return false;
    }
  }

  Future<void> getBenficiaries({required chamaId}) async {
    isgetRes(true);
    update();
    try {
      var res = await apiProvider.request(
        url: "${ApiUrls.getbeneficiaries}/$chamaId",
        method: Method.GET,
      );
      if (res.data["status"]) {
        beneficiaries([]);
        for (var item in res.data["data"]["next_beneficiaries"]) {
          beneficiaries.add(NextBeneficiary.fromJson(item));
        }
      }
      isgetRes(false);
    } catch (e) {
      isgetRes(false);

      apiMessage(e.toString());
    }
  }

  Future<void> getChamaTrnsactions(
      {required int chamaId,
      int page = 0,
      int? size = 20,
      String? startDate,
      String? endDate,
      String? code,
      String? accountNo,
      String? category}) async {
    isGetChamaTransactionLoading(true);
    try {
      String url =
          "${ApiUrls.getChamaTransactions}?chama_id=$chamaId&&page=$page&&size=$size";
      if (!(startDate?.isEmpty ?? true) && !(endDate?.isEmpty ?? true)) {
        url += "&start_date=$startDate&end_date=$endDate";
      } else if (!(accountNo?.isEmpty ?? true)) {
        url += "&account_number=$accountNo";
      } else if (!(code?.isEmpty ?? true)) {
        url += "&transaction_code=$code";
      } else if (!(category?.isEmpty ?? true)) {
        url += "&category=$category";
      }
      var res = await apiProvider.request(url: url, method: Method.GET);
      apiMessage(res.data["message"]);
      logger.log(Level.debug, res.data);
      if (res.data["status"]) {
        chamaTransactions.clear();
        for (var element in res.data["data"]["items"] ?? []) {
          chamaTransactions.add(TransactionModel.fromJson(element));
        }
        TransDts fetchedResults = TransDts.fromJson(res.data["data"]);
        results.value = fetchedResults;
      }
      isGetChamaTransactionLoading(false);
    } catch (e) {
      isGetChamaTransactionLoading(false);
      logger.e(e);
      apiMessage(e.toString());
    }
  }

  void reorderChamaMembers(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    final item = membersOrder.removeAt(oldIndex);
    membersOrder.insert(newIndex, item);
    update();
  }

  void shuffleChamaMembers() {
    membersOrder.shuffle();
    update();
  }

  Future<bool> postTransactionText(
      {required ChamaTextTransactions requst}) async {
    isPostTextTransactionLoading(true);
    try {
      var res = await apiProvider.request(
          url: ApiUrls.postTransactionText,
          method: Method.POST,
          params: requst.toJson());
      apiMessage(res.data["message"]);
      if (res.data["status"]) {
        apiMessage(res.data["message"]);
        transMessage(res.data["data"]);
      }
      isPostTextTransactionLoading(false);
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      isPostTextTransactionLoading(false);
      apiMessage('An error occured');
      return false;
    }
  }

  Future<bool> chamaContribute(
      {required ChamaContributeRequest request,
      required bool isConfirm}) async {
    isChamaContributionLoading(true);
    try {
      var res = await apiProvider.request(
          url: isConfirm ? ApiUrls.chamaContrConfirm : ApiUrls.chamaContribute,
          method: Method.POST,
          params: request.toJson());
      apiMessage(res.data["message"]);
      logger.log(Level.debug, res.data);
      if (res.data["status"] && !isConfirm) {
        var data = res.data["data"];
        chamaTitle(data["chama_title"]);
        ctrAccNo(data["account_number"]);
        ctrAmt(data["amount"]);
        penaltyName(data["penalty_title"]);
        memberNames(data["member_names"]);
      }
      if (res.data["status"] && isConfirm) {
        contributeData(res.data["data"]);
      }
      memPenaltiesUsingAcc.clear();
      hasPenalties(false);
      isChamaContributionLoading(false);
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      isChamaContributionLoading(false);
      return false;
    }
  }

  Future<void> getMemberPenaltiesUsingAcc(
      {required String accNo, required int kittyId}) async {
    isGetMemberPenUsingAccNo(true);
    accountNo.value = accNo;
    chamaId.value = kittyId;
    // Reset penalties state at the beginning
    hasPenalties(false);
    memPenaltiesUsingAcc([]);

    try {
      var res = await apiProvider.request(
          url:
              "${ApiUrls.checkMemberPenalties}?account_number=$accNo&kitty_id=$kittyId",
          method: Method.GET);

      if (res.data["status"] &&
          res.data["data"] != null &&
          res.data["data"].length > 0) {
        for (var element in res.data["data"]) {
          memPenaltiesUsingAcc.add(MemPenalty.fromJson(element));
        }
        // Only set hasPenalties to true if we actually have penalties
        if (memPenaltiesUsingAcc.isNotEmpty) {
          hasPenalties(true);
        }
      }

      apiMessage(res.data["message"]);
    } catch (e) {
      logger.e(e);
      apiMessage(e.toString());
    } finally {
      // Always set loading to false in finally block to ensure UI updates
      isGetMemberPenUsingAccNo(false);
    }
  }
}

class ChamaDataController extends GetxController {
  Rx<UserChama> chama = UserChama().obs;
  Rx<ChamaMembers> members = ChamaMembers().obs;
  Rx<Chama> singleChamaDts = Chama().obs;
  Rx<PenaltyModel> penalty = PenaltyModel().obs;
  Rx<NextBeneficiary> beneficiaries = NextBeneficiary().obs;
  Rx<NextBeneficiary> singleBenf = NextBeneficiary().obs;
  Rx<ResourceDts> singleRs = ResourceDts().obs;
  RxBool isloading = false.obs;
  RxBool status = true.obs;
  RxString apiMessage = "".obs;
  RxInt fetchKittyId = 0.obs;

  Rx<ChamaFromKitty> chamaFromKitty = ChamaFromKitty().obs;
  // final chamaMedia = <KittyMediaModel>[].obs;
  final HttpService apiProvider = Get.put(HttpService());

//Bank
  int channel = 0;
  final Rx<String> channelName = ''.obs;
  final Rx<PaymentChannels?> selectedBank = Rx<PaymentChannels?>(null);
  void setChannel(String name, int code) {
    channel = code;
    channelName(name);
  }

  Future<void> getAllChamaDetailsWithKittyId({required int kittyId}) async {
    try {
      isloading(true);
      update();
      var res = await apiProvider.request(
          url: ApiUrls.getKittyChamaDetails + kittyId.toString(),
          method: Method.GET);
      apiMessage(res.data["message"]);
      status(res.data["status"]);
      update();
      if (res.data["status"]) {
        chamaFromKitty(ChamaFromKitty.fromJson(res.data["data"]));
        fetchKittyId(kittyId);
      }
      isloading(false);
      update();
    } catch (e) {
      isloading(false);
      apiMessage("An error occured");
      update();
    }
  }
}
