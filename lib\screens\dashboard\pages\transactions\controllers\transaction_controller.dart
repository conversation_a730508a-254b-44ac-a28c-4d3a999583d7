// Unified Transaction Controller
// Manages state and business logic for all transaction types

import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import '../models/transaction_type.dart';
import '../services/transaction_service.dart';

class TransactionController extends GetxController {
  final TransactionService _transactionService = Get.find<TransactionService>();
  final Logger _logger = Get.find<Logger>();

  // Observable properties
  final RxList<TransactionModel> _transactions = <TransactionModel>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isLoadingMore = false.obs;
  final RxInt _currentPage = 1.obs; // Start from page 1 instead of 0
  final RxBool _hasMoreData = true.obs;
  final RxInt _totalPages = 1.obs;
  final RxInt _totalTransactions = 0.obs;

  // Filter properties - these will trigger backend API calls
  final RxString _searchFilter = ''.obs;
  final RxString _transactionCodeFilter = ''.obs;
  final RxString _startDateFilter = ''.obs;
  final RxString _endDateFilter = ''.obs;

  // Configuration
  late TransactionPageConfig _config;
  final int _pageSize = 20;

  // Getters
  List<TransactionModel> get transactions => _transactions.toList();
  bool get isLoading => _isLoading.value;
  bool get isLoadingMore => _isLoadingMore.value;
  bool get hasMoreData => _hasMoreData.value;
  TransactionPageConfig get config => _config;
  int get currentPage => _currentPage.value;
  int get totalPages => _totalPages.value;
  int get totalTransactions => _totalTransactions.value;

  // Filter getters
  String get searchFilter => _searchFilter.value;
  String get transactionCodeFilter => _transactionCodeFilter.value;
  String get startDateFilter => _startDateFilter.value;
  String get endDateFilter => _endDateFilter.value;



  /// Initialize controller with configuration
  void initialize(TransactionPageConfig config) {
    _config = config;
    _resetState();
    loadTransactions();
  }

  /// Load transactions based on configuration
  Future<void> loadTransactions({bool refresh = false}) async {
    _logger.i('Loading ${_config.transactionType.name} transactions (refresh: $refresh)');

    if (refresh) {
      _currentPage.value = 1;
      _hasMoreData.value = true;
      _transactions.clear();
    }

    if (_isLoading.value) return;

    _isLoading.value = true;

    try {
      List<TransactionModel> newTransactions = await _fetchTransactionsForType();
      _logger.i('Fetched ${newTransactions.length} new transactions');

      _transactions.assignAll(newTransactions);
      _hasMoreData.value = newTransactions.length >= _pageSize;

      // Estimate total pages based on current data
      if (newTransactions.length < _pageSize) {
        _totalPages.value = _currentPage.value;
      } else {
        _totalPages.value = _currentPage.value + 1; // At least one more page
      }

      _logger.i('Current page: ${_currentPage.value}, Total pages: ${_totalPages.value}');
    } catch (e) {
      _logger.e('Error loading transactions: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load more transactions (pagination)
  Future<void> loadMoreTransactions() async {
    if (_isLoadingMore.value || !_hasMoreData.value) return;

    _isLoadingMore.value = true;

    try {
      List<TransactionModel> newTransactions = await _fetchTransactionsForType();
      _transactions.addAll(newTransactions);
      
      _hasMoreData.value = newTransactions.length >= _pageSize;
      _currentPage.value++;
    } catch (e) {
      _logger.e('Error loading more transactions: $e');
    } finally {
      _isLoadingMore.value = false;
    }
  }

  /// Fetch transactions based on type
  Future<List<TransactionModel>> _fetchTransactionsForType() async {
    // Use currentPage - 1 because API expects 0-based indexing
    final apiPage = _currentPage.value - 1;

    switch (_config.transactionType) {
      case TransactionType.user:
        final userController = Get.find<UserKittyController>();
        final user = userController.getLocalUser();
        return await _transactionService.fetchTransactions(
          type: TransactionType.user,
          phoneNumber: user?.phoneNumber,
          page: apiPage,
          size: _pageSize,
          transactionCode: _transactionCodeFilter.value.isEmpty ? null : _transactionCodeFilter.value,
          startDate: _startDateFilter.value.isEmpty ? null : _startDateFilter.value,
          endDate: _endDateFilter.value.isEmpty ? null : _endDateFilter.value,
          search: _searchFilter.value.isEmpty ? null : _searchFilter.value,
        );

      case TransactionType.kitty:
        // Use TransactionService for consistent filtering support
        return await _transactionService.fetchTransactions(
          type: TransactionType.kitty,
          entityId: _config.entityId,
          page: apiPage,
          size: _pageSize,
          transactionCode: _transactionCodeFilter.value.isEmpty ? null : _transactionCodeFilter.value,
          startDate: _startDateFilter.value.isEmpty ? null : _startDateFilter.value,
          endDate: _endDateFilter.value.isEmpty ? null : _endDateFilter.value,
          search: _searchFilter.value.isEmpty ? null : _searchFilter.value,
        );

      case TransactionType.chama:
        return await _transactionService.fetchTransactions(
          type: TransactionType.chama,
          entityId: _config.entityId,
          accountNo: _config.accountNo,
          page: apiPage,
          size: _pageSize,
          transactionCode: _transactionCodeFilter.value.isEmpty ? null : _transactionCodeFilter.value,
          startDate: _startDateFilter.value.isEmpty ? null : _startDateFilter.value,
          endDate: _endDateFilter.value.isEmpty ? null : _endDateFilter.value,
          search: _searchFilter.value.isEmpty ? null : _searchFilter.value,
        );

      case TransactionType.event:
        return await _transactionService.fetchTransactions(
          type: TransactionType.event,
          entityId: _config.entityId,
          page: apiPage,
          size: _pageSize,
          transactionCode: _transactionCodeFilter.value.isEmpty ? null : _transactionCodeFilter.value,
          startDate: _startDateFilter.value.isEmpty ? null : _startDateFilter.value,
          endDate: _endDateFilter.value.isEmpty ? null : _endDateFilter.value,
          search: _searchFilter.value.isEmpty ? null : _searchFilter.value,
        );
    }
  }

  /// Set search filter and reload transactions
  void setSearchFilter(String query) {
    _logger.i('Setting search filter: $query');
    _searchFilter.value = query;
    loadTransactions(refresh: true);
  }



  /// Set transaction code filter and reload transactions
  void setTransactionCodeFilter(String code) {
    _logger.i('Setting transaction code filter: $code');
    _transactionCodeFilter.value = code;
    loadTransactions(refresh: true);
  }

  /// Set date filters and reload transactions
  void setDateFilters(String? startDate, String? endDate) {
    _logger.i('Setting date filters: startDate=$startDate, endDate=$endDate');
    _startDateFilter.value = startDate ?? '';
    _endDateFilter.value = endDate ?? '';
    loadTransactions(refresh: true);
  }

  /// Clear all filters and reload transactions
  void clearAllFilters() {
    _logger.i('Clearing all filters');
    _searchFilter.value = '';
    _transactionCodeFilter.value = '';
    _startDateFilter.value = '';
    _endDateFilter.value = '';
    loadTransactions(refresh: true);
  }

  /// Legacy method for backward compatibility - now uses search filter
  void searchTransactions(String query) {
    setSearchFilter(query);
  }



  /// Go to specific page
  Future<void> goToPage(int page) async {
    if (page < 1 || page == _currentPage.value || _isLoading.value) return;

    _currentPage.value = page;
    await loadTransactions();
  }

  /// Go to next page
  Future<void> nextPage() async {
    if (_hasMoreData.value) {
      await goToPage(_currentPage.value + 1);
    }
  }

  /// Go to previous page
  Future<void> previousPage() async {
    if (_currentPage.value > 1) {
      await goToPage(_currentPage.value - 1);
    }
  }

  /// Reset state
  void _resetState() {
    _transactions.clear();
    _searchFilter.value = '';
    _transactionCodeFilter.value = '';
    _startDateFilter.value = '';
    _endDateFilter.value = '';
    _currentPage.value = 1;
    _hasMoreData.value = true;
    _isLoading.value = false;
    _isLoadingMore.value = false;
    _totalPages.value = 1;
    _totalTransactions.value = 0;
  }

  /// Refresh transactions
  Future<void> refreshTransactions() async {
    await loadTransactions(refresh: true);
  }

  /// Get transaction count
  int get transactionCount => _transactions.length;

  /// Check if transactions are empty
  bool get isEmpty {
    return _transactions.isEmpty && !_isLoading.value;
  }

  @override
  void onClose() {
    _resetState();
    super.onClose();
  }
}
